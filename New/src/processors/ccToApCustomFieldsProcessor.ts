/**
 * CC to AP Custom Fields Processor
 *
 * Handles synchronization of custom fields from CliniCore (CC) to AutoPatient (AP) by:
 * 1. Fetching CC patient custom field data using the custom field IDs
 * 2. Filtering out standard contact fields to prevent conflicts
 * 3. Mapping CC custom field values to AP custom field format with proper Unicode handling
 * 4. Creating AP custom fields with appropriate data types if they don't exist
 * 5. Updating the AP contact with the synchronized custom field values
 *
 * Features:
 * - Unicode-aware field name matching for international characters
 * - Data type mapping between CC and AP custom field types
 * - Prevention of standard contact field conversion to custom fields
 * - Proper handling of multiple values in CC custom fields
 */

import type {
	APGetCustomFieldType,
	APPostCustomfieldType,
	GetCCCustomField,
	GetCCPatientCustomField,
	GetCCPatientType,
} from "@type";
import { apCustomfield, contactReq, patientReq } from "@/apiClient";
import { logApiError } from "@/utils/errorLogger";

/**
 * Standard contact fields that should not be converted to custom fields
 * Based on PostAPContactType interface to prevent conflicts with core contact data
 */
const STANDARD_CONTACT_FIELDS = [
	"email",
	"phone",
	"name",
	"firstName",
	"lastName",
	"timezone",
	"dnd",
	"source",
	"assignedTo",
	"address1",
	"city",
	"state",
	"country",
	"postalCode",
	"tags",
	"dateOfBirth",
	"ssn",
	"gender",
	"customFields",
	// Common variations and translations
	"first name",
	"last name",
	"date of birth",
	"phone number",
	"email address",
	"postal code",
	"zip code",
	"address",
	"vorname",
	"nachname",
	"geburtsdatum",
	"telefon",
	"e-mail",
	"adresse",
	"postleitzahl",
];

/**
 * CC to AP data type mapping
 * Maps CliniCore custom field types to AutoPatient data types
 */
const CC_TO_AP_DATA_TYPE_MAPPING: Record<string, string> = {
	// Text-based fields
	text: "TEXT",
	textarea: "LARGE_TEXT",
	string: "TEXT",

	// Numeric fields
	number: "NUMERICAL",
	integer: "NUMERICAL",
	decimal: "FLOAT",
	float: "FLOAT",
	currency: "MONETORY",
	money: "MONETORY",

	// Contact fields
	phone: "PHONE",
	telephone: "PHONE",

	// Boolean fields
	boolean: "CHECKBOX",
	checkbox: "CHECKBOX",

	// Selection fields
	select: "SINGLE_OPTIONS",
	dropdown: "SINGLE_OPTIONS",
	radio: "SINGLE_OPTIONS",
	multiselect: "MULTIPLE_OPTIONS",

	// Date/Time fields
	date: "DATE",
	datetime: "DATE",
	time: "TIME",

	// File fields
	file: "FILE_UPLOAD",
	upload: "FILE_UPLOAD",
	attachment: "FILE_UPLOAD",

	// Signature
	signature: "SIGNATURE",

	// Default fallback
	default: "TEXT",
};

/**
 * Interface for custom field mapping result
 */
interface CustomFieldMapping {
	/** AP custom field ID */
	id: string;
	/** Field value to set */
	value: string;
}

/**
 * Normalize string for Unicode-aware comparison
 * Handles German Umlaut characters and other special characters
 */
function normalizeFieldName(name: string): string {
	return name
		.toLowerCase()
		.normalize("NFD")
		.replace(/[\u0300-\u036f]/g, "") // Remove diacritics
		.replace(/[^\w\s]/g, "") // Remove special characters except word chars and spaces
		.replace(/\s+/g, " ") // Normalize whitespace
		.trim();
}

/**
 * Check if two field names match using Unicode-aware comparison
 */
function fieldNamesMatch(name1: string, name2: string): boolean {
	const normalized1 = normalizeFieldName(name1);
	const normalized2 = normalizeFieldName(name2);
	return normalized1 === normalized2;
}

/**
 * Check if a field name represents a standard contact field
 */
function isStandardContactField(fieldName: string): boolean {
	const normalizedFieldName = normalizeFieldName(fieldName);
	return STANDARD_CONTACT_FIELDS.some(standardField =>
		normalizeFieldName(standardField) === normalizedFieldName
	);
}

/**
 * Map CC custom field type to AP data type
 */
function mapCcToApDataType(ccFieldType: string): string {
	const normalizedType = ccFieldType.toLowerCase().trim();
	return CC_TO_AP_DATA_TYPE_MAPPING[normalizedType] || CC_TO_AP_DATA_TYPE_MAPPING.default;
}

/**
 * Extract and convert CC allowed values to AP textBoxListOptions format
 * @param ccField - CC custom field with allowedValues
 * @param currentValue - Current field value to include as an option if not in allowedValues
 * @returns Array of textBoxListOptions for AP custom field
 */
function extractTextBoxListOptions(
	ccField: GetCCCustomField,
	currentValue: string,
): { label: string; prefillValue: string }[] {
	const options: { label: string; prefillValue: string }[] = [];

	// Extract allowed values from CC field
	if (ccField.allowedValues && ccField.allowedValues.length > 0) {
		for (const allowedValue of ccField.allowedValues) {
			if (allowedValue.value && allowedValue.value.trim() !== "") {
				options.push({
					label: allowedValue.value.trim(),
					prefillValue: allowedValue.value.trim(),
				});
			}
		}
	}

	// Ensure current value is included as an option if it's not already present
	if (currentValue && currentValue.trim() !== "") {
		const currentValueTrimmed = currentValue.trim();
		const existingOption = options.find(
			option => option.label.toLowerCase() === currentValueTrimmed.toLowerCase()
		);

		if (!existingOption) {
			options.push({
				label: currentValueTrimmed,
				prefillValue: currentValueTrimmed,
			});
		}
	}

	// If no options were found, create a default option based on current value
	if (options.length === 0 && currentValue && currentValue.trim() !== "") {
		options.push({
			label: currentValue.trim(),
			prefillValue: currentValue.trim(),
		});
	}

	return options;
}

/**
 * Extract and combine multiple values from CC custom field
 * Handles different separation strategies based on field type
 */
function extractFieldValues(ccCustomField: GetCCPatientCustomField): string {
	if (!ccCustomField.values || ccCustomField.values.length === 0) {
		return "";
	}

	const values = ccCustomField.values
		.map(v => v.value)
		.filter((v): v is string => v != null && v.trim() !== "");

	if (values.length === 0) {
		return "";
	}

	// For single value, return as-is
	if (values.length === 1) {
		return values[0];
	}

	// For multiple values, choose separation strategy based on field type
	const fieldType = ccCustomField.field.type?.toLowerCase() || "";

	if (fieldType.includes("multiselect") || fieldType.includes("checkbox")) {
		// Use comma separation for multi-select fields
		return values.join(", ");
	} else if (fieldType.includes("textarea") || fieldType.includes("text")) {
		// Use newline separation for text areas
		return values.join("\n");
	} else {
		// Default to comma separation
		return values.join(", ");
	}
}

/**
 * Synchronize custom fields from CC patient to AP contact
 *
 * @param requestId - Request ID from Hono context for logging correlation
 * @param localPatientId - Local database patient ID for logging context
 * @param ccPatientData - CC patient data containing custom field IDs
 * @param apContactId - AP contact ID to update with custom fields
 * @returns Promise<void> - Completes sync or throws error
 */
export async function syncCcToApCustomFields(
	requestId: string,
	localPatientId: string,
	ccPatientData: GetCCPatientType,
	apContactId: string,
): Promise<void> {

	console.log(
		`[${requestId}] Starting custom field sync for CC Patient ${ccPatientData.id} -> AP Contact ${apContactId} (Local Patient ID: ${localPatientId})`,
	);

	// Step 1: Check if patient has custom fields
	if (!ccPatientData.customFields || ccPatientData.customFields.length === 0) {
		console.log(
			`[${requestId}] No custom fields found for CC patient ${ccPatientData.id}`,
		);
		return;
	}

	try {
		// Step 2: Fetch CC patient custom field data
		console.log(
			`[${requestId}] Fetching ${ccPatientData.customFields.length} custom fields from CC`,
		);
		const ccPatientCustomFields = await patientReq.customFields(
			ccPatientData.customFields,
		);

		if (!ccPatientCustomFields || ccPatientCustomFields.length === 0) {
			console.log(`[${requestId}] No custom field data returned from CC`);
			return;
		}

		// Step 3: Filter out excluded fields and extract valid field mappings
		const validCustomFields = await filterAndMapCustomFields(
			ccPatientCustomFields,
			requestId,
		);

		if (validCustomFields.length === 0) {
			console.log(
				`[${requestId}] No valid custom fields to sync after filtering`,
			);
			return;
		}

		// Step 4: Get all AP custom fields for mapping
		console.log(`[${requestId}] Fetching AP custom fields for mapping`);
		const apCustomFields = await apCustomfield.all();

		// Step 5: Map CC fields to AP format and create missing fields
		const apCustomFieldMappings = await mapToApCustomFields(
			validCustomFields,
			apCustomFields,
			requestId,
		);

		if (apCustomFieldMappings.length === 0) {
			console.log(`[${requestId}] No custom field mappings created`);
			return;
		}

		// Step 6: Update AP contact with custom fields
		console.log(
			`[${requestId}] Updating AP contact with ${apCustomFieldMappings.length} custom fields`,
		);
		await contactReq.update(apContactId, {
			customFields: apCustomFieldMappings,
		});

		console.log(`[${requestId}] Custom field sync completed successfully`);
	} catch (error) {
		console.error(`[${requestId}] Custom field sync failed:`, error);

		// Log the error but don't throw to avoid blocking main patient processing
		await logApiError(
			error as Error,
			requestId,
			"custom_field_sync",
			"cc_to_ap_sync",
			{
				ccPatientId: ccPatientData.id,
				apContactId,
				customFieldCount: ccPatientData.customFields?.length || 0,
			},
		);

		// Re-throw to let caller decide how to handle
		throw error;
	}
}

/**
 * Filter CC custom fields and extract valid field mappings
 * Excludes standard contact fields and empty values
 *
 * @param ccPatientCustomFields - CC patient custom field data
 * @param requestId - Request ID for logging
 * @returns Promise<Array> - Valid custom field data
 */
async function filterAndMapCustomFields(
	ccPatientCustomFields: GetCCPatientCustomField[],
	requestId: string,
): Promise<Array<{ field: GetCCCustomField; value: string }>> {
	const validFields: Array<{ field: GetCCCustomField; value: string }> = [];

	for (const ccCustomField of ccPatientCustomFields) {
		const fieldName = ccCustomField.field.name;
		const fieldLabel = ccCustomField.field.label;

		// Check if field is a standard contact field that should not be converted to custom field
		if (isStandardContactField(fieldName) || isStandardContactField(fieldLabel)) {
			console.log(
				`[${requestId}] Excluding standard contact field: ${fieldName} (${fieldLabel})`,
			);
			continue;
		}

		// Extract field value using improved multiple values handling
		const fieldValue = extractFieldValues(ccCustomField);

		if (!fieldValue || fieldValue.trim() === "") {
			console.log(`[${requestId}] Skipping field with empty value: ${fieldName}`);
			continue;
		}

		validFields.push({
			field: ccCustomField.field,
			value: fieldValue,
		});

		console.log(
			`[${requestId}] Valid field: ${fieldName} (${fieldLabel}) = ${fieldValue.substring(0, 100)}${fieldValue.length > 100 ? '...' : ''}`,
		);
	}

	return validFields;
}

/**
 * Map CC custom fields to AP custom field format
 * Creates missing AP custom fields with proper data types as needed
 *
 * @param validCustomFields - Filtered CC custom field data
 * @param apCustomFields - Existing AP custom fields
 * @param requestId - Request ID for logging
 * @returns Promise<CustomFieldMapping[]> - AP custom field mappings
 */
async function mapToApCustomFields(
	validCustomFields: Array<{ field: GetCCCustomField; value: string }>,
	apCustomFields: APGetCustomFieldType[],
	requestId: string,
): Promise<CustomFieldMapping[]> {
	const mappings: CustomFieldMapping[] = [];

	for (const { field, value } of validCustomFields) {
		try {
			// Try to find existing AP custom field using Unicode-aware matching
			let apCustomField = apCustomFields.find((apField) =>
				fieldNamesMatch(apField.name, field.name) ||
				fieldNamesMatch(apField.name, field.label)
			);

			// Create AP custom field if it doesn't exist
			if (!apCustomField) {
				const mappedDataType = mapCcToApDataType(field.type);

				console.log(
					`[${requestId}] Creating new AP custom field: ${field.label} (type: ${field.type} -> ${mappedDataType})`,
				);

				const createData: APPostCustomfieldType = {
					name: field.label, // Use label as it's more user-friendly
					dataType: mappedDataType,
					model: "contact", // Set model to contact as required
				};

				// Add conditional properties based on data type
				if (mappedDataType === "SINGLE_OPTIONS" || mappedDataType === "MULTIPLE_OPTIONS") {
					// Extract allowed values from CC field and convert to AP format
					const textBoxListOptions = extractTextBoxListOptions(field, value);

					if (textBoxListOptions.length > 0) {
						createData.textBoxListOptions = textBoxListOptions;
						console.log(
							`[${requestId}] Adding ${textBoxListOptions.length} options to ${field.label}: ${textBoxListOptions.map(opt => opt.label).join(", ")}`,
						);

						// Debug: Log the exact structure of textBoxListOptions
						console.log(
							`[${requestId}] textBoxListOptions structure for ${field.label}:`,
							JSON.stringify(textBoxListOptions, null, 2)
						);
					} else {
						// If no options can be created, fall back to TEXT type to avoid API error
						console.log(
							`[${requestId}] No options available for ${field.label}, falling back to TEXT type`,
						);
						createData.dataType = "TEXT";
						// Remove textBoxListOptions if we're falling back to TEXT
						delete createData.textBoxListOptions;
					}
				}

				// Debug: Log the complete payload being sent to the API
				console.log(
					`[${requestId}] Complete API payload for ${field.label}:`,
					JSON.stringify(createData, null, 2)
				);

				try {
					apCustomField = await apCustomfield.create(createData);
					console.log(
						`[${requestId}] Successfully created AP custom field: ${field.label} with ID: ${apCustomField.id}`
					);
				} catch (apiError) {
					console.error(
						`[${requestId}] API Error creating custom field ${field.label}:`,
						apiError
					);

					// Log the full error details for debugging
					if (apiError instanceof Error) {
						console.error(`[${requestId}] Error message: ${apiError.message}`);
						console.error(`[${requestId}] Error stack: ${apiError.stack}`);
					}

					// If it's a SINGLE_OPTIONS field and the error mentions options, try alternative approaches
					if (mappedDataType === "SINGLE_OPTIONS" &&
						apiError instanceof Error &&
						apiError.message.includes("options")) {

						console.log(
							`[${requestId}] Options error detected. Trying alternative approaches for ${field.label}`
						);

						// Try approach 1: Remove the model field
						const altData1: APPostCustomfieldType = {
							name: field.label,
							dataType: mappedDataType,
							textBoxListOptions: createData.textBoxListOptions,
						};

						console.log(
							`[${requestId}] Attempt 1: Trying without model field:`,
							JSON.stringify(altData1, null, 2)
						);

						try {
							apCustomField = await apCustomfield.create(altData1);
							console.log(
								`[${requestId}] Success with approach 1 (no model): ${field.label} with ID: ${apCustomField.id}`
							);
						} catch (altError1) {
							console.error(`[${requestId}] Approach 1 failed:`, altError1);

							// Try approach 2: Use "options" instead of "textBoxListOptions"
							const altData2 = {
								name: field.label,
								dataType: mappedDataType,
								model: "contact",
								options: createData.textBoxListOptions,
							};

							console.log(
								`[${requestId}] Attempt 2: Trying with 'options' field:`,
								JSON.stringify(altData2, null, 2)
							);

							try {
								apCustomField = await apCustomfield.create(altData2 as APPostCustomfieldType);
								console.log(
									`[${requestId}] Success with approach 2 (options field): ${field.label} with ID: ${apCustomField.id}`
								);
							} catch (altError2) {
								console.error(`[${requestId}] Approach 2 failed:`, altError2);

								// Final fallback: Create as TEXT field
								console.log(
									`[${requestId}] All SINGLE_OPTIONS approaches failed. Falling back to TEXT type for ${field.label}`
								);

								const fallbackData: APPostCustomfieldType = {
									name: field.label,
									dataType: "TEXT",
									model: "contact",
								};

								try {
									apCustomField = await apCustomfield.create(fallbackData);
									console.log(
										`[${requestId}] Successfully created fallback TEXT field: ${field.label} with ID: ${apCustomField.id}`
									);
								} catch (fallbackError) {
									console.error(
										`[${requestId}] Even TEXT fallback failed for ${field.label}:`,
										fallbackError
									);
									throw fallbackError;
								}
							}
						}
					} else {
						throw apiError;
					}
				}

				// Add to our local cache to avoid duplicate creation
				apCustomFields.push(apCustomField);
			}

			mappings.push({
				id: apCustomField.id,
				value: value,
			});

			console.log(
				`[${requestId}] Mapped: ${field.label} -> AP Field ID ${apCustomField.id} (${value.substring(0, 50)}${value.length > 50 ? '...' : ''})`,
			);
		} catch (error) {
			console.error(`[${requestId}] Failed to map field ${field.name}:`, error);
			// Continue with other fields rather than failing completely
		}
	}

	return mappings;
}
